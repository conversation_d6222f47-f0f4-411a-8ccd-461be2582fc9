{"version": 3, "file": "static.js", "sourceRoot": "", "sources": ["../../src/static.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,EAGL,cAAc,IAAI,cAAc,GACjC,MAAM,cAAc,CAAC;AAGtB;;;;;;;GAOG;AACH,SAAS,MAAM,CACb,IAAgB,EAChB,GAA4C,EAC5C,OAAwB;IAExB,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAC;IAErB,OAAO,IAAI,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC/E,CAAC;AAED;;;;;;GAMG;AACH,SAAS,SAAS,CAChB,GAAyD,EACzD,OAAwB;IAExB,OAAO,CACL,CAAC,OAAO;QACR,OAAO,GAAG,KAAK,QAAQ;QACvB,GAAG,IAAI,IAAI;QACX,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC;QAClB,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,CACjB,CAAC;AACJ,CAAC;AAuBD,MAAM,UAAU,IAAI,CAElB,GAAkD,EAClD,OAAwB;IAExB;;;;;OAKG;IACH,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAErE;;;OAGG;IACH,MAAM,IAAI,GAAG;QACX,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ;QACjB,GAAG,cAAc,CAAC,OAAO,CAAC;KAC3B,CAAC;IAEF,OAAO,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACtC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,GAAG,CAEjB,GAAiC;IAEjC,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAEpD,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACpC,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,IAAI,CAElB,QAA6B;IAE7B,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAEpD,IAAI,GAAG,GAAG,EAAE,CAAC;IAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAsBD,MAAM,UAAU,SAAS,CAEvB,IAAoB,EACpB,OAAiB,EACjB,WAAW,GAAG,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;IAE5D,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;QACjC,WAAW,GAAG,OAAO,CAAC;IACxB,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACrD,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,QAAQ,CAAC,SAAkB,EAAE,SAAkB;IAC7D,oEAAoE;IACpE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,IAAI,IAAI,GAAmB,SAAS,CAAC;IACrC,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QACpC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACnB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,OAAO,CAErB,GAAM;IAEN,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAID;;;;;;;;;GASG;AACH,MAAM,UAAU,KAAK,CACnB,IAA4B,EAC5B,IAAkB;IAElB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,OAAO;IACT,CAAC;IACD,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;IAC5B,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IACxB,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,WAAW,CAAC,IAAa;IAChC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IACE,OAAO,IAAI,KAAK,QAAQ;QACxB,IAAI,KAAK,IAAI;QACb,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC;QACnB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;QAC/B,IAAI,CAAC,MAAM,GAAG,CAAC,EACf,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}