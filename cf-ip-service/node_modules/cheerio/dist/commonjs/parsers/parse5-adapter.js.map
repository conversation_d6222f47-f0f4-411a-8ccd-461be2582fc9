{"version": 3, "file": "parse5-adapter.js", "sourceRoot": "", "sources": ["../../../src/parsers/parse5-adapter.ts"], "names": [], "mappings": ";;AAmBA,0CAeC;AAUD,4CAqBC;AAjED,2CAKoB;AACpB,mCAA+E;AAC/E,qFAAgF;AAGhF;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,OAAe,EACf,OAAwB,EACxB,UAAmB,EACnB,OAA0B;;IAE1B,MAAA,OAAO,CAAC,WAAW,oCAAnB,OAAO,CAAC,WAAW,GAAK,yCAAkB,EAAC;IAE3C,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;QACvC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,OAAO,UAAU;QACf,CAAC,CAAC,IAAA,cAAa,EAAC,OAAO,EAAE,OAAO,CAAC;QACjC,CAAC,CAAC,IAAA,sBAAa,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,yCAAkB,EAAE,CAAC;AAEvD;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,GAAiC;IAChE;;;;OAIG;IACH,MAAM,KAAK,GAAG,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAA,uBAAU,EAAC,IAAI,CAAC,EAAE,CAAC;YACrB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,MAAM,IAAI,IAAA,uBAAc,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}