{"clientTcpRtt": 6, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "NA", "asn": 5650, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "US", "isEUCountry": false, "region": "California", "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "tOKURqmLwLx35ZjI7pX4/vPKPFqn3PEnMsIZ6/goZGw=", "tlsExportedAuthenticator": {"clientFinished": "75d5b9832296d61c549b012dc26fdbfd79de866ef1e59768ac9c7d57f776bad239b2501cadfd691e61fcd61f262b6084", "clientHandshake": "85d44d4fdb4c011f20ab41b05eccc2490652e6b14c91626957c71e2ecde61e4439b12d50f524cb2c80de9d2cd2d8cbcb", "serverHandshake": "dedc89e6f1438ab6552352773c270326a277e98a5570ef7c477270b41a6a01aecbc74cd626fb48b34093b9d3222cb096", "serverFinished": "580d8c17939c792ef538044e467fe02661840000b885ac8ab16947eeb9ff083a6cac78f1613639bf48c5ca34aadefb63"}, "tlsClientHelloLength": "1605", "colo": "LAX", "timezone": "America/Los_Angeles", "longitude": "-117.96868", "latitude": "33.99307", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "91745", "city": "Hacienda Heights", "tlsVersion": "TLSv1.3", "regionCode": "CA", "asOrganization": "Frontier Communications of America, Inc.", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}