/**
 * 调试端点 - 检查 KV 绑定和数据状态
 */
export async function onRequest(context) {
    try {
        const { env } = context;
        
        let debugInfo = {
            timestamp: new Date().toISOString(),
            kvBinding: !!env.IPDB_KV,
            environment: env.ENVIRONMENT || 'unknown'
        };
        
        if (env.IPDB_KV) {
            try {
                // 尝试读取一些测试数据
                const testKeys = ['bestcf', 'cfv4', 'last_updated'];
                const kvData = {};
                
                for (const key of testKeys) {
                    try {
                        const value = await env.IPDB_KV.get(key);
                        kvData[key] = value ? `${value.length} chars` : 'null';
                    } catch (error) {
                        kvData[key] = `Error: ${error.message}`;
                    }
                }
                
                debugInfo.kvData = kvData;
                
                // 尝试写入测试数据
                try {
                    await env.IPDB_KV.put('debug_test', 'test_value_' + Date.now());
                    debugInfo.kvWriteTest = 'success';
                } catch (error) {
                    debugInfo.kvWriteTest = `Error: ${error.message}`;
                }
                
            } catch (error) {
                debugInfo.kvError = error.message;
            }
        }
        
        return new Response(JSON.stringify(debugInfo, null, 2), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
        
    } catch (error) {
        return new Response(JSON.stringify({
            error: error.message,
            stack: error.stack
        }, null, 2), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}