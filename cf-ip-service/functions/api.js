/**
 * 这个函数处理所有指向 /api/* 的请求。
 * 它是一个轻量级的代理，负责从 KV 中读取数据并返回给用户。
 */
export async function onRequest(context) {
    try {
        const { request, env } = context;
        const url = new URL(request.url);
        const params = url.searchParams;

        // 处理 OPTIONS 请求（CORS 预检）
        if (request.method === 'OPTIONS') {
            return new Response(null, {
                status: 200,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            });
        }

        // 只允许 GET 请求
        if (request.method !== 'GET') {
            return new Response('Method not allowed. Only GET requests are supported.', {
                status: 405,
                headers: { 'Content-Type': 'text/plain' }
            });
        }

        // 1. 解析 URL 查询参数
        // 'type' 参数可以包含多个值，用分号分隔，例如: type=cfv4;cfv6
        const types = params.get('type')?.split(';').filter(Boolean) || [];
        const showCountry = params.get('country') === 'true';
        const download = params.get('down') === 'true';

        // 如果没有提供 'type' 参数，返回帮助信息
        if (types.length === 0) {
            return new Response(getHelpMessage(), {
                status: 400,
                headers: { 'Content-Type': 'text/plain; charset=utf-8' }
            });
        }

        // 验证 KV 绑定
        const KV = env.IPDB_KV;
        if (!KV) {
            console.error("❌ IPDB_KV binding not found");
            return new Response("Service temporarily unavailable. Please try again later.", {
                status: 503,
                headers: { 'Content-Type': 'text/plain' }
            });
        }

        // 2. 验证请求的类型是否有效
        const validTypes = ['cfv4', 'cfv6', 'proxy', 'bestcf', 'bestproxy', 'proxyip'];
        const invalidTypes = types.filter(type => !validTypes.includes(type));
        if (invalidTypes.length > 0) {
            return new Response(`Error: Invalid type(s): ${invalidTypes.join(', ')}. Valid types: ${validTypes.join(', ')}`, {
                status: 400,
                headers: { 'Content-Type': 'text/plain' }
            });
        }

        // 3. 从 KV 中异步获取所有请求的数据
        const promises = types.map(async (type) => {
            try {
                // 根据 'country' 参数决定要查询的 Key
                const key = showCountry ? `${type}_country` : type;
                const data = await KV.get(key);
                
                if (!data) {
                    console.warn(`⚠️ No data found for key: ${key}`);
                    return null;
                }
                
                return data;
            } catch (error) {
                console.error(`❌ Error fetching ${type}:`, error);
                return null;
            }
        });
        
        // 并发执行所有 KV 读取操作
        const results = await Promise.all(promises);

        // 4. 合并结果
        // .filter(Boolean) 会过滤掉那些 KV.get 返回 null (即 Key 不存在) 的结果
        const validResults = results.filter(Boolean);
        
        if (validResults.length === 0) {
            return new Response("No data available for the requested types. The data might not be ready yet.", {
                status: 404,
                headers: { 'Content-Type': 'text/plain' }
            });
        }

        const body = validResults.join('\n');

        // 5. 构建响应头
        const headers = {
            'Content-Type': 'text/plain; charset=utf-8',
            // 允许跨域请求，这样我们的 Web UI 才能在浏览器中调用此 API
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Cache-Control': 'max-age=60', // 建议客户端缓存结果 60 秒
            'X-Total-IPs': body.split('\n').filter(line => line.trim()).length.toString(),
            'X-Data-Types': types.join(';'),
            'X-Last-Updated': await getLastUpdated(KV)
        };

        // 如果 'down=true'，添加文件下载头
        if (download) {
            const filename = `ipdb_${types.join('_')}${showCountry ? '_country' : ''}_${new Date().toISOString().split('T')[0]}.txt`;
            headers['Content-Disposition'] = `attachment; filename="${filename}"`;
        }

        return new Response(body, { headers });

    } catch (error) {
        console.error("❌ API Error:", error);
        return new Response("An internal server error occurred.", { 
            status: 500,
            headers: { 'Content-Type': 'text/plain' }
        });
    }
}

/**
 * 获取帮助信息
 * @returns {string} 帮助文本
 */
function getHelpMessage() {
    return `🚀 高性能 IP 服务 API

用法:
  GET /api?type=<类型>[&country=true][&down=true]

参数:
  type     - 必需。IP类型，支持多个值用分号分隔
             可选值: cfv4, cfv6, proxy, bestcf, bestproxy, proxyip
  country  - 可选。设为 true 时返回带国家信息的IP列表
  down     - 可选。设为 true 时触发文件下载

示例:
  /api?type=bestcf
  /api?type=bestcf&country=true
  /api?type=cfv4;bestproxy
  /api?type=proxyip&down=true

支持的IP类型:
  - cfv4: Cloudflare IPv4 地址段
  - cfv6: Cloudflare IPv6 地址段  
  - proxy: 代理服务器IP列表
  - bestcf: 最佳Cloudflare IP
  - bestproxy: 最佳代理IP
  - proxyip: 补充代理IP（来源：nslookup.io）`;
}

/**
 * 获取最后更新时间
 * @param {Object} KV - KV存储实例
 * @returns {Promise<string>} 最后更新时间
 */
async function getLastUpdated(KV) {
    try {
        const lastUpdated = await KV.get('last_updated');
        return lastUpdated || 'Unknown';
    } catch (error) {
        console.error('Error getting last_updated:', error);
        return 'Unknown';
    }
}