/**
 * 使用备用数据源和测试数据填充 KV 存储
 */
export async function onRequest(context) {
    try {
        const { env } = context;
        const KV = env.IPDB_KV;
        
        if (!KV) {
            return new Response('KV binding not found', { status: 500 });
        }
        
        console.log('🔄 Populating KV with backup data sources...');
        
        // Cloudflare 官方 IP 段 (这些是公开的固定数据)
        const cfv4Data = `************/20
************/22
************/22
**********/22
************/18
*************/18
************/20
************/20
*************/22
************/17
***********/15
**********/13
**********/14
**********/13
**********/22`;

        const cfv6Data = `2400:cb00::/32
2606:4700::/32
2803:f800::/32
2405:b500::/32
2405:8100::/32
2a06:98c0::/29
2c0f:f248::/32`;

        // 一些常用的优选 IP (示例数据)
        const bestcfData = `*************
**************
**************
*************
*************
**************
**************
**************
**************
**************`;

        const proxyData = `************
***************
************
**************
*************
**************
***************
***************
**************
**************`;

        const bestproxyData = `***********
************
*************
***************
**************
**************
***********
**************
*************
*************`;

        // 尝试从备用 API 获取数据
        let updatedFromApi = false;
        try {
            // 尝试使用 Cloudflare 官方 API
            const cfApiResponse = await fetch('https://www.cloudflare.com/ips-v4', {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (compatible; IP-Service/1.0)'
                }
            });
            
            if (cfApiResponse.ok) {
                const cfApiData = await cfApiResponse.text();
                if (cfApiData.trim()) {
                    await KV.put('cfv4', cfApiData.trim());
                    console.log('✅ Updated cfv4 from Cloudflare official API');
                    updatedFromApi = true;
                }
            }
        } catch (error) {
            console.log('⚠️ Cloudflare API not available, using backup data');
        }
        
        // 如果 API 不可用，使用备用数据
        if (!updatedFromApi) {
            await KV.put('cfv4', cfv4Data);
            console.log('✅ Used backup cfv4 data');
        }
        
        // 存储其他数据
        await KV.put('cfv6', cfv6Data);
        await KV.put('bestcf', bestcfData);
        await KV.put('proxy', proxyData);
        await KV.put('bestproxy', bestproxyData);
        await KV.put('proxyip', proxyData); // 使用相同的代理数据
        
        // 创建带国家信息的版本
        const cfv4CountryData = cfv4Data.split('\n').map(ip => `${ip}#US`).join('\n');
        const bestcfCountryData = bestcfData.split('\n').map(ip => `${ip}#US`).join('\n');
        const proxyCountryData = proxyData.split('\n').map((ip, i) => {
            const countries = ['HK', 'SG', 'JP', 'KR', 'US', 'DE', 'UK', 'FR', 'CA', 'AU'];
            return `${ip}#${countries[i % countries.length]}`;
        }).join('\n');
        
        await KV.put('cfv4_country', cfv4CountryData);
        await KV.put('cfv6_country', cfv6Data.split('\n').map(ip => `${ip}#US`).join('\n'));
        await KV.put('bestcf_country', bestcfCountryData);
        await KV.put('proxy_country', proxyCountryData);
        await KV.put('bestproxy_country', proxyCountryData);
        await KV.put('proxyip_country', proxyCountryData);
        
        // 更新时间戳
        await KV.put('last_updated', new Date().toISOString());
        
        console.log('🎉 KV population completed successfully');
        
        // 验证数据
        const verification = {};
        const keys = ['cfv4', 'cfv6', 'bestcf', 'proxy', 'bestproxy', 'proxyip'];
        for (const key of keys) {
            const data = await KV.get(key);
            verification[key] = data ? `${data.split('\n').length} IPs` : 'null';
        }
        
        return new Response(JSON.stringify({
            status: 'success',
            message: 'KV store populated with backup data',
            timestamp: new Date().toISOString(),
            verification: verification,
            note: 'Using backup data sources due to API restrictions'
        }, null, 2), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
        
    } catch (error) {
        console.error('❌ Population failed:', error);
        return new Response(JSON.stringify({
            status: 'error',
            error: error.message
        }, null, 2), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}