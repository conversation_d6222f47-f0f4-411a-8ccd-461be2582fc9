import * as cheerio from 'cheerio';

/**
 * 这个函数会被 Cloudflare 根据 Cron 计划自动触发。
 * 它的职责是作为数据管道，获取、处理并存储 IP 数据。
 */
export default {
  async scheduled(event, env, ctx) {
    return await onCron({ env, ctx });
  }
};

/**
 * 这个函数会被 Cloudflare 根据 Cron 计划自动触发。
 * 它的职责是作为数据管道，获取、处理并存储 IP 数据。
 */
export async function onCron(context) {
    console.log("🚀 Cron job started: Fetching and updating IP data into KV...");

    const { env } = context;
    const KV = env.IPDB_KV;

    if (!KV) {
        console.error("❌ IPDB_KV binding not found. Please check wrangler.toml configuration.");
        return;
    }

    try {
        // === 任务 1: 从 Cloudflare 官方源获取数据 (保持不变) ===
        console.log("📡 Fetching official Cloudflare IP ranges...");
        const officialIpsV4 = await fetch('https://www.cloudflare.com/ips-v4');
        if (officialIpsV4.ok) {
            const text = await officialIpsV4.text();
            await KV.put('cfv4', text);
            await KV.put('cfv4_country', text.split('\n').filter(Boolean).map(ip => `${ip}#CF-Official`).join('\n'));
            console.log("✅ Successfully updated cfv4 from official source.");
        }

        const officialIpsV6 = await fetch('https://www.cloudflare.com/ips-v6');
        if (officialIpsV6.ok) {
            const text = await officialIpsV6.text();
            await KV.put('cfv6', text);
            await KV.put('cfv6_country', text.split('\n').filter(Boolean).map(ip => `${ip}#CF-Official`).join('\n'));
            console.log("✅ Successfully updated cfv6 from official source.");
        }

        // === 任务 2: 从我们的反向代理获取增值数据 (新增) ===
        console.log("🛰️ Fetching value-added IP lists from our reverse proxy...");
        const ipdbProxyBase = 'https://ipdb.123go.eu.org:8443/';
        const proxiedTypes = ['proxy', 'bestcf', 'bestproxy'];

        for (const type of proxiedTypes) {
            try {
                // 获取不带国家信息的 IP 列表
                const plainResponse = await fetch(`${ipdbProxyBase}?type=${type}`);
                if (plainResponse.ok) {
                    await KV.put(type, await plainResponse.text());
                    console.log(`✅ Successfully updated ${type} from proxy.`);
                } else {
                    console.warn(`⚠️ Failed to fetch ${type} from proxy. Status: ${plainResponse.status}`);
                }

                // 获取带国家信息的 IP 列表
                const countryResponse = await fetch(`${ipdbProxyBase}?type=${type}&country=true`);
                if (countryResponse.ok) {
                    await KV.put(`${type}_country`, await countryResponse.text());
                    console.log(`✅ Successfully updated ${type}_country from proxy.`);
                } else {
                    console.warn(`⚠️ Failed to fetch ${type}_country from proxy. Status: ${countryResponse.status}`);
                }
            } catch (error) {
                console.error(`❌ Error fetching ${type} from proxy:`, error.message);
            }
        }
        
        // === 任务 3: 抓取补充代理 IP 数据 (保持不变) ===
        console.log("🕷️ Starting web scraping from nslookup.io...");
        const scrapeUrl = 'https://www.nslookup.io/domains/bpb.yousef.isegaro.com/dns-records/';
        try {
            const scrapeResponse = await fetch(scrapeUrl);
            if (scrapeResponse.ok) {
                const html = await scrapeResponse.text();
                const $ = cheerio.load(html);
                const proxyIpList = [];
                const proxyIpCountryList = [];
                $('div.dns-record').each((i, el) => {
                    const ip = $(el).find('span.dns-record-value-ip').text().trim();
                    const location = $(el).find('a[href*="google.com/maps/search"]').text().trim();
                    if (ip && location) {
                        proxyIpList.push(ip);
                        const country = location.split(', ').pop();
                        proxyIpCountryList.push(`${ip}#${country}`);
                    }
                });
                if (proxyIpList.length > 0) {
                    await KV.put('proxyip', proxyIpList.join('\n'));
                    await KV.put('proxyip_country', proxyIpCountryList.join('\n'));
                    console.log(`✅ Successfully updated KV for scraped type: proxyip (${proxyIpList.length} IPs found)`);
                }
            } else {
                console.warn("⚠️ Web scraping from nslookup.io failed.");
            }
        } catch (error) {
            console.error("❌ Error during nslookup.io scraping:", error.message);
        }

        // === 任务 4: 更新最后更新时间戳 ===
        const timestamp = new Date().toISOString();
        await KV.put('last_updated', timestamp);

        console.log("🎉 Cron job finished: KV update successful.");

    } catch (error) {
        console.error("❌ Cron job failed with a critical error:", error);
    }
}


/**
 * 简单的 IP 地址验证
 * @param {string} ip - 要验证的 IP 地址
 * @returns {boolean} - 是否为有效的 IP 地址
 */
function isValidIP(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * 延迟函数
 * @param {number} ms - 延迟的毫秒数
 * @returns {Promise<void>}
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}