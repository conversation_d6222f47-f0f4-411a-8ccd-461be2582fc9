import { onCron } from './_scheduled.js';

/**
 * 手动触发数据更新的端点 - 带详细日志
 */
export async function onRequest(context) {
    const logs = [];
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    
    // 拦截 console 输出
    console.log = (...args) => {
        logs.push(`LOG: ${args.join(' ')}`);
        originalConsoleLog(...args);
    };
    console.error = (...args) => {
        logs.push(`ERROR: ${args.join(' ')}`);
        originalConsoleError(...args);
    };
    console.warn = (...args) => {
        logs.push(`WARN: ${args.join(' ')}`);
        originalConsoleWarn(...args);
    };
    
    try {
        const { request, env } = context;
        
        if (!['GET', 'POST'].includes(request.method)) {
            return new Response('Method not allowed', { status: 405 });
        }
        
        logs.push('🔄 Manual data update triggered...');
        
        // 执行数据更新
        await onCron(context);
        
        // 恢复原始 console
        console.log = originalConsoleLog;
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
        
        // 检查更新后的数据
        const KV = env.IPDB_KV;
        const testKeys = ['bestcf', 'cfv4', 'proxy'];
        const dataStatus = {};
        
        for (const key of testKeys) {
            const value = await KV.get(key);
            dataStatus[key] = value ? `${value.length} chars` : 'null';
        }
        
        const result = {
            status: 'completed',
            timestamp: new Date().toISOString(),
            logs: logs,
            dataStatus: dataStatus
        };
        
        return new Response(JSON.stringify(result, null, 2), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
        
    } catch (error) {
        // 恢复原始 console
        console.log = originalConsoleLog;
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
        
        logs.push(`FATAL ERROR: ${error.message}`);
        
        return new Response(JSON.stringify({
            status: 'failed',
            error: error.message,
            logs: logs
        }, null, 2), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}